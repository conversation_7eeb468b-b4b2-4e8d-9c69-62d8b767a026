import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

const NotesPage = () => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  // Note creation removed as requested
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredNotes, setFilteredNotes] = useState([]);

  useEffect(() => {
    loadNotes();
  }, []);

  useEffect(() => {
    if (notes.length > 0) {
      filterNotes();
    }
  }, [searchQuery, notes]);

  const loadNotes = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {},
        "GET"
      );

      if (!response.error) {
        // Sort notes by created_at in descending order (latest first)
        const sortedNotes = (response.list || []).sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at);
        });
        setNotes(sortedNotes);
        setFilteredNotes(sortedNotes);
      } else {
        setError(response.message);
      }
    } catch (err) {
      console.error("Failed to load notes:", err);
      setError(err.message || "Failed to load notes");
      showToast(globalDispatch, err.message || "Failed to load notes", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const filterNotes = () => {
    if (!searchQuery.trim()) {
      setFilteredNotes(notes);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = notes.filter(note =>
      note.description.toLowerCase().includes(query) ||
      note.title.toLowerCase().includes(query) ||
      (note.referral_title && note.referral_title.toLowerCase().includes(query)) ||
      (note.referral_description && note.referral_description.toLowerCase().includes(query))
    );

    setFilteredNotes(filtered);
  };

  // Note creation functionality removed as requested

  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted note from the list
        setNotes(prev => prev.filter(note => note.id !== noteId));
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      console.error("Error deleting note:", err);
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#eaeaea]">My Notes</h1>
        <p className="text-[#b5b5b5]">
          Create and manage your personal notes here.
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <div className="relative w-64">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search notes..."
            className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea]"
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#b5b5b5]">
            <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none">
              <path
                d="M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Note creation form removed as requested */}

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-[#2e7d32] border-t-transparent"></div>
        </div>
      ) : filteredNotes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredNotes.map((note) => (
            <div
              key={note.id}
              className="rounded-lg border border-[#363636] bg-[#242424] p-4"
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="text-[#eaeaea] whitespace-pre-wrap break-words">
                    {note.description}
                  </p>

                  {/* Display referral details if available */}
                  {note.referral_id && (
                    <div className="mt-3 p-2 rounded border-l-4 border-[#2e7d32] bg-[#1a1a1a]">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <p className="text-xs text-[#b5b5b5] mb-1">Referral name</p>
                          <p className="text-sm text-[#eaeaea] font-medium">{note.referral_title}</p>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          note.referral_status === 'completed'
                            ? 'bg-green-800 text-green-200'
                            : note.referral_status === 'archived'
                            ? 'bg-gray-700 text-gray-200'
                            : note.referral_status === 'active'
                            ? 'bg-blue-800 text-blue-200'
                            : 'bg-yellow-800 text-yellow-200'
                        }`}>
                          {note.referral_status}
                        </span>
                      </div>
                      <button
                        onClick={() => navigate(`/member/referrals/${note.referral_id}/details`)}
                        className="flex items-center gap-1 text-xs text-[#7dd87d] hover:text-[#2e7d32] transition-colors"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        View Referral
                      </button>
                    </div>
                  )}

                  <p className="mt-2 text-sm text-[#b5b5b5]">
                    {formatDate(note.created_at)}
                  </p>
                </div>
                <button
                  onClick={() => handleDeleteNote(note.id)}
                  className="ml-2 text-[#b5b5b5] hover:text-[#dc3545]"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12">
          <svg className="h-16 w-16 text-[#363636]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="mt-4 text-[#b5b5b5]">
            {searchQuery ? "No notes found matching your search." : "You don't have any notes yet. Create your first note!"}
          </p>
        </div>
      )}
    </div>
  );
};

export default NotesPage;
