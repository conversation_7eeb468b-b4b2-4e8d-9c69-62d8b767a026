import { lazy } from "react";

// Public
export const PublicPage = lazy(() => {
  const __import = import("../pages/Public/PublicPage");
  __import.finally(() => {});
  return __import;
});


// ADMIN PAGES

export const AdminListUserPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListUserPage");
  __import.finally(() => {});
  return __import;
});
export const AdminViewPaymentPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewPaymentPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUserPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUserPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUserPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewUserPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListUploadsPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUploadsPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUploadsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUploadsPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewUploadsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListJobPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddJobPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditJobPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewJobPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewJobPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListTokensPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddTokensPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditTokensPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewTokensPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewTokensPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListPreferencePage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddPreferencePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditPreferencePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewPreferencePage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewPreferencePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_productPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_productPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_productPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditStripe_productPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_productPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewStripe_productPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListStripe_pricePage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddStripe_pricePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditStripe_pricePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditStripe_pricePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStripe_pricePage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewStripe_pricePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCommunityPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCommunityPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCommunityPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddCommunityPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCommunityPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditCommunityPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCommunityPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewCommunityPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListIndustryPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListIndustryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddIndustryPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddIndustryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditIndustryPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditIndustryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewIndustryPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewIndustryPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCommunity_memberPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCommunity_memberPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCommunity_memberPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddCommunity_memberPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCommunity_memberPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditCommunity_memberPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCommunity_memberPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewCommunity_memberPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCommunity_join_requestPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCommunity_join_requestPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCommunity_join_requestPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddCommunity_join_requestPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCommunity_join_requestPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditCommunity_join_requestPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCommunity_join_requestPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewCommunity_join_requestPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCommunity_invitePage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCommunity_invitePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCommunity_invitePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddCommunity_invitePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCommunity_invitePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditCommunity_invitePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCommunity_invitePage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewCommunity_invitePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListReferralPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListReferralPage");
  __import.finally(() => {});
  return __import;
});
export const AdminListPaymentsPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListPaymentsPage");
  __import.finally(() => {});
  return __import;
});
export const AdminListPlansPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListPlansPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewPlansPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewPlansPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddReferralPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddReferralPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditReferralPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditReferralPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewReferralPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewReferralPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListReferral_communitiesPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListReferral_communitiesPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddReferral_communitiesPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddReferral_communitiesPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditReferral_communitiesPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditReferral_communitiesPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewReferral_communitiesPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewReferral_communitiesPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListActivity_feedPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListActivity_feedPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddActivity_feedPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddActivity_feedPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditActivity_feedPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditActivity_feedPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewActivity_feedPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewActivity_feedPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListMeetingsPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListMeetingsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddMeetingsPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddMeetingsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditMeetingsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditMeetingsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewMeetingsPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewMeetingsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListMeeting_attendeePage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListMeeting_attendeePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddMeeting_attendeePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddMeeting_attendeePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditMeeting_attendeePage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditMeeting_attendeePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewMeeting_attendeePage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewMeeting_attendeePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListIntegrationPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListIntegrationPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddIntegrationPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddIntegrationPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditIntegrationPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditIntegrationPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewIntegrationPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewIntegrationPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListAnalytics_eventsPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListAnalytics_eventsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddAnalytics_eventsPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddAnalytics_eventsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditAnalytics_eventsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditAnalytics_eventsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewAnalytics_eventsPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewAnalytics_eventsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCommissionPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCommissionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCommissionPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddCommissionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCommissionPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/AdminEditCommissionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCommissionPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewCommissionPage");
  __import.finally(() => {});
  return __import;
});

// ADMIN SETTINGS PAGE
export const AdminSettingsPage = lazy(() => {
  const __import = import("../pages/Admin/Settings/AdminSettingsPage");
  __import.finally(() => {});
  return __import;
});

// ADMIN PAGES ENDS


// MAGIC LOGIN PAGES

export const UserMagicLoginPage = lazy(() => {
  const __import = import("../pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("../pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

// MAGIC LOGIN PAGES ENDS

// USER PAGES

export const UserAddUserTablePage = lazy(() => {
  const __import = import("../pages/User/Add/UserAddUserTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserSignUpPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const UserForgotPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserForgotPage");
  __import.finally(() => {});
  return __import;
});

export const UserResetPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserResetPage");
  __import.finally(() => {});
  return __import;
});

export const UserDashboardPage = lazy(() => {
  const __import = import("../pages/User/View/UserDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const UserLoginPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserLoginPage");
  __import.finally(() => {});
  return __import;
});

export const UserListUserTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListUserTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserProfilePage = lazy(() => {
  const __import = import("../pages/User/View/UserProfilePage");
  __import.finally(() => {});
  return __import;
});

export const UserViewUserTablePage = lazy(() => {
  const __import = import("../pages/User/View/UserViewUserTablePage");
  __import.finally(() => {});
  return __import;
});

export const SingleReferralPage = lazy(() => {
  const __import = import("../pages/User/Referrals/SingleReferralPage");
  __import.finally(() => {});
  return __import;
});

// USER PAGES ENDS

// STRIPE PAGES

export const AddAdminStripePricePage = lazy(() => {
  const __import = import("../pages/stripe/AddAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminStripeProductPage = lazy(() => {
  const __import = import("../pages/stripe/AddAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeChargesListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeChargesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeInvoicesListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeInvoicesListPage");
  __import.finally(() => {});
  return __import;
});


export const AdminStripeOrdersListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeOrdersListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripePricesListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripePricesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeProductsListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeProductsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeSubscriptionsListPage = lazy(() => {
  const __import = import(
    "../pages/stripe/AdminStripeSubscriptionsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const EditAdminStripePricePage = lazy(() => {
  const __import = import("../pages/stripe/EditAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminStripeProductPage = lazy(() => {
  const __import = import("../pages/stripe/EditAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});

// STRIPE PAGES ENDS